import { test, expect } from '@playwright/test'

test.beforeEach(async ({ page }) => {
  await page.goto('https://testautomationpractice.blogspot.com/');
});

//Upload a single file using the Choose File button
test('Verify that the user can upload a single file using the Choose File button', async ({ page }) => {
    await page.locator('#singleFileInput').click();
    await expect(page.locator('input[type="file"]')).toBeVisible();


}); 